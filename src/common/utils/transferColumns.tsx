/* eslint-disable @typescript-eslint/naming-convention */
// 处理从接口出获取列表的schema
import { ReactNode } from 'react';
import DescTooltip from '@components/Table/components/DescTooltip';
import EnumPopover from '@components/Table/components/EnumPopover';
import TagListPopover from '@components/Table/components/TagListPopover';
import Avatar from '@components/Table/components/Avatar';
import { ResColumnsProps } from '@common/constants/listConfig';
import { DescOptions, OptionItem } from '@http_idl/infoService';
import { Tag, Typography } from '@hi-design/ui';
import StringTooltip from '@components/Table/components/StringTooltip';
import {
  formatSchemaWithStarlingKey,
  ManageType,
  SchemaType,
} from './starlingKey';
import { Color } from '@hi-design/ui/es/components/tag/types';
import { Link } from '@edenx/plugin-router-v5/runtime';

interface RenderColumnsProps {
  title?: string;
  dataIndex?: string;
  width: number;
  fixed?: boolean | string;
  render?: any;
}
interface RenderItemProps {
  value: string;
  desc?: string;
  color?: string;
  descOptions?: DescOptions;
  options?: Array<OptionItem>;
}

// 为特定类型指定对应组件
const compType = {
  status_tag_single: (text: RenderItemProps): ReactNode => (
    <Tag color={(text.color as Color) || ('green' as Color)}>{text.value}</Tag>
  ),
  desc_string: (text: RenderItemProps): ReactNode => (
    <DescTooltip value={text.value} desc={text.desc} />
  ),
  popover_string_enum: (text: RenderItemProps): ReactNode => (
    <EnumPopover value={text.value} descOptions={text.descOptions} />
  ),
  tooltip_string: (text: RenderItemProps): ReactNode => (
    <StringTooltip value={text.value} />
  ),
  popover_string_list: (text: RenderItemProps): ReactNode => (
    <TagListPopover value={text.value} options={text.options} />
  ),
  avatar: (text: RenderItemProps): ReactNode => <Avatar value={text.value} />,
};

// 为特定类型指定自定义渲染方式
const renderComp = (
  text: RenderItemProps,
  item: ResColumnsProps,
): ReactNode => {
  /*
    如果某个title没有对应的data，相应的title下显示‘-’
    如果某个data中的value为‘’，相应的title下显示‘-’
    如果没有定义某个data中的value，相应的title下显示‘-’
   */
  if (!text?.value || text.value === '-') {
    return <>-</>;
  }
  const comp = compType[item.type](text, item);
  return comp ? comp : <div>{text.value}</div>; // 如果某一title的type未定义，默认为string类型
};

/**
 * @description: 转换数据格式
 * @param {string} schema: 从接口处获取的列表schema
 * @param {ManageType} manageType 用于判断是INFO的选项schema还是API的选项schema，用于获取多语言starling Key
 * @return {*}
 */
const transferColumns = (
  schema: string,
  manageType: ManageType,
): Array<RenderColumnsProps> => {
  const data: RenderColumnsProps[] = [];
  try {
    const ColumnsData = formatSchemaWithStarlingKey({
      schema,
      schemaType: SchemaType.PAGE,
      manageType,
    });
    ColumnsData.length &&
      ColumnsData.forEach(item => {
        if (!item.key) {
          return;
        }
        const obj: RenderColumnsProps = {
          // 如果返回的某个title值没有，展示‘-’
          title: item.label || '-',
          dataIndex: item.key,
          width: item.props?.width || 200,
        };
        item.props?.fixed && (obj.fixed = true);
        // 如果是特殊类型，则需要自定义渲染
        if (item.type !== 'string') {
          obj.render = (text: RenderItemProps): ReactNode =>
            renderComp(text, item);
        } else if (item.key === 'apiId') {
          obj.render = (text: string): ReactNode => (
            <Typography.Text link>
              <Link
                to={`/api/detail?id=${text}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                {text}
              </Link>
            </Typography.Text>
          );
        }
        data.push(obj);
      });
  } catch (err) {
    console.log('transferColumns err:', err);
  }
  return data;
};

export default transferColumns;
