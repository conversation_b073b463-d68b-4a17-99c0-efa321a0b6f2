// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as info_service from './namespaces/info_service';

export { base, info_service };

export type Int64 = string;

export default class InfoServiceRpcService<T> {
  private request: any = () => {
    throw new Error('InfoServiceRpcService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    request?<R>(
      params: {
        rpc: string;
        data?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=405493)
   *
   * 批量获取Info
   */
  BatchGetInfo(
    req: info_service.BatchGetInfoRequest,
    options?: T,
  ): Promise<info_service.GetInfoResponse> {
    const rpc = 'BatchGetInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=405494)
   *
   * 获取Action
   */
  GetAction(
    req: info_service.GetActionRequest,
    options?: T,
  ): Promise<info_service.GetActionResponse> {
    const rpc = 'GetAction';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=405495)
   *
   * 根据Info获取Query
   */
  GetQueryByInfo(
    req: info_service.GetQueryByInfoRequest,
    options?: T,
  ): Promise<info_service.GetQueryByInfoResponse> {
    const rpc = 'GetQueryByInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=405496)
   *
   * 根据action获取Query
   */
  GetQueryByAction(
    req: info_service.GetQueryByActionRequest,
    options?: T,
  ): Promise<info_service.GetQueryByActionResponse> {
    const rpc = 'GetQueryByAction';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=405497)
   *
   * 获取init&normal Info
   */
  GetInfo(
    req?: info_service.GetInfoRequest,
    options?: T,
  ): Promise<info_service.GetInfoResponse> {
    const rpc = 'GetInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=405498)
   *
   * 根据关键词搜索Info
   */
  SearchInfo(
    req: info_service.SearchInfoRequest,
    options?: T,
  ): Promise<info_service.GetInfoResponse> {
    const rpc = 'SearchInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=428110)
   *
   * info配置校验
   */
  GetInfoConfigCheck(
    req: info_service.GetInfoConfigCheckRequest,
    options?: T,
  ): Promise<info_service.GetInfoConfigCheckResponse> {
    const rpc = 'GetInfoConfigCheck';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=495149)
   *
   * 获取Api
   */
  GetApi(
    req?: info_service.GetApiRequest,
    options?: T,
  ): Promise<info_service.GetApiResponse> {
    const rpc = 'GetApi';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497302)
   *
   * Info/API新增/修改接口
   */
  UpdateOrAddData(
    req: info_service.UpdateOrAddDataRequest,
    options?: T,
  ): Promise<info_service.UpdateOrAddDataResponse> {
    const rpc = 'UpdateOrAddData';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497303)
   *
   * 获取API/INFO表单schema和表单数据
   */
  GetInsertOrUpdateSchema(
    req: info_service.GetInsertOrUpdateSchemaRequest,
    options?: T,
  ): Promise<info_service.GetInsertOrUpdateSchemaResponse> {
    const rpc = 'GetInsertOrUpdateSchema';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497304)
   *
   * 获取API列表页数据
   */
  GetApiDataList(
    req: info_service.GetApiDataListRequest,
    options?: T,
  ): Promise<info_service.GetApiDataListResponse> {
    const rpc = 'GetApiDataList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497305)
   *
   * Info/API操作记录查询接口
   */
  GetOperateRecordList(
    req: info_service.GetOperateRecordListRequest,
    options?: T,
  ): Promise<info_service.GetOperateRecordListResponse> {
    const rpc = 'GetOperateRecordList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497306)
   *
   * 获取Info列表/筛选项Scheme
   */
  GetInfoSelectPageScheme(
    req?: info_service.GetInfoSelectPageSchemeRequest,
    options?: T,
  ): Promise<info_service.GetInfoSelectPageSchemeResponse> {
    const rpc = 'GetInfoSelectPageScheme';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497307)
   *
   * 获取API列表/筛选项Scheme
   */
  GetApiSelectPageScheme(
    req?: info_service.GetApiSelectPageSchemeRequest,
    options?: T,
  ): Promise<info_service.GetApiSelectPageSchemeResponse> {
    const rpc = 'GetApiSelectPageScheme';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497308)
   *
   * 禁用/启用Info/API
   */
  DisableOrEnableInfoOrApi(
    req: info_service.DisableOrEnableInfoOrApiRequest,
    options?: T,
  ): Promise<info_service.DisableOrEnableInfoOrApiResponse> {
    const rpc = 'DisableOrEnableInfoOrApi';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=497309)
   *
   * 获取Info列表页数据
   */
  GetInfoDataList(
    req: info_service.GetInfoDataListRequest,
    options?: T,
  ): Promise<info_service.GetInfoDataListResponse> {
    const rpc = 'GetInfoDataList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=554888)
   *
   * 获取Info初始入参
   */
  GetInfoInitParams(
    req: info_service.GetInfoInitParamsRequest,
    options?: T,
  ): Promise<info_service.GetInfoInitParamsResponse> {
    const rpc = 'GetInfoInitParams';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=592673)
   *
   * 搜索流转人
   */
  SearchTransferUser(
    req: info_service.SearchTransferUserRequest,
    options?: T,
  ): Promise<info_service.SearchTransferUserResponse> {
    const rpc = 'SearchTransferUser';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=592674)
   *
   * 删除编辑态INFO
   */
  DeleteInfo(
    req: info_service.DeleteInfoRequest,
    options?: T,
  ): Promise<info_service.DeleteInfoResponse> {
    const rpc = 'DeleteInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=592675)
   *
   * 保存INFO
   */
  SaveInfo(
    req: info_service.SaveInfoRequest,
    options?: T,
  ): Promise<info_service.SaveInfoResponse> {
    const rpc = 'SaveInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=592676)
   *
   * 流转INFO
   */
  TransferInfo(
    req: info_service.TransferInfoRequest,
    options?: T,
  ): Promise<info_service.TransferInfoResponse> {
    const rpc = 'TransferInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=682102)
   *
   * 删除编辑态API
   */
  DeleteApi(
    req: info_service.DeleteApiRequest,
    options?: T,
  ): Promise<info_service.DeleteApiResponse> {
    const rpc = 'DeleteApi';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=682103)
   *
   * 保存API
   */
  SaveApi(
    req: info_service.SaveApiRequest,
    options?: T,
  ): Promise<info_service.SaveApiResponse> {
    const rpc = 'SaveApi';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=731807)
   *
   * 通过InfoId获取GQL调试链接
   */
  GetGraphQLDebugLink(
    req?: info_service.GetGraphQLDebugLinkRequest,
    options?: T,
  ): Promise<info_service.GetGraphQLDebugLinkResponse> {
    const rpc = 'GetGraphQLDebugLink';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=830257)
   *
   * V2新增
   *
   * info配置页面获取gql链接
   */
  GetGqlLink(
    req: info_service.GetGqlLinkRequest,
    options?: T,
  ): Promise<info_service.GetGqlLinkResponse> {
    const rpc = 'GetGqlLink';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=830258)
   *
   * info配置页面获取测试数据
   */
  GetInfoTestData(
    req: info_service.GetInfoTestDataRequest,
    options?: T,
  ): Promise<info_service.GetInfoTestDataResponse> {
    const rpc = 'GetInfoTestData';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=830259)
   *
   * info配置页面展示运行结果
   */
  GetInfoTestResult(
    req: info_service.GetInfoTestResultRequest,
    options?: T,
  ): Promise<info_service.GetInfoTestResultResponse> {
    const rpc = 'GetInfoTestResult';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=923496)
   *
   * 仅特征使用
   *
   * 获取特征的业务线和场景
   */
  GetFeatureBusinessLineAndScene(
    req?: info_service.GetFeatureBusinessLineAndSceneRequest,
    options?: T,
  ): Promise<info_service.GetFeatureBusinessLineAndSceneResponse> {
    const rpc = 'GetFeatureBusinessLineAndScene';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932712)
   *
   * 入参格式获取
   */
  GetInputFormatList(
    req: info_service.GetInputFormatListRequest,
    options?: T,
  ): Promise<info_service.GetInputFormatListResponse> {
    const rpc = 'GetInputFormatList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932713)
   *
   * api应用渠道、接入方搜索
   */
  QueryApiApplicationsAndAccessParty(
    req: info_service.QueryApiApplicationsAndAccessPartyRequest,
    options?: T,
  ): Promise<info_service.QueryApiApplicationsAndAccessPartyResponse> {
    const rpc = 'QueryApiApplicationsAndAccessParty';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932714)
   *
   * 读取TCC配置通用接口
   */
  GetConfig(
    req: info_service.GetConfigRequest,
    options?: T,
  ): Promise<info_service.GetConfigResponse> {
    const rpc = 'GetConfig';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932715)
   *
   * V3新增
   *
   * Psm推荐列表查询
   */
  QueryApiPsmPrefixList(
    req: info_service.QueryApiPsmPrefixListRequest,
    options?: T,
  ): Promise<info_service.QueryApiPsmPrefixListResponse> {
    const rpc = 'QueryApiPsmPrefixList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932716)
   *
   * Psm下对应Method列表查询
   */
  QueryMethodListByPsm(
    req: info_service.QueryMethodListByPsmRequest,
    options?: T,
  ): Promise<info_service.QueryMethodListByPsmResponse> {
    const rpc = 'QueryMethodListByPsm';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932717)
   *
   * 获取info/api编辑页更新数据的回显数据
   */
  GetUpdateData(
    req: info_service.GetUpdateDataRequest,
    options?: T,
  ): Promise<info_service.GetUpdateDataResponse> {
    const rpc = 'GetUpdateData';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=932718)
   *
   * InfoKey列表搜索
   */
  QueryInfoKeyList(
    req: info_service.QueryInfoKeyListRequest,
    options?: T,
  ): Promise<info_service.QueryInfoKeyListResponse> {
    const rpc = 'QueryInfoKeyList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=990621)
   *
   * 返回有问题的api和对应infoKey（api函数名重复以及入参格式参数比初始入参多）
   */
  QueryApiAndInfoKeyListWithRepeatOrWrong(
    req?: info_service.QueryApiAndInfoKeyListWithRepeatOrWrongRequest,
    options?: T,
  ): Promise<info_service.QueryApiAndInfoKeyListWithRepeatOrWrongResponse> {
    const rpc = 'QueryApiAndInfoKeyListWithRepeatOrWrong';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=1045593)
   *
   * 根据InfoKeys获取对应API的Variables
   */
  GetVariablesByInfoKeys(
    req: info_service.GetVariablesByInfoKeysRequest,
    options?: T,
  ): Promise<info_service.GetVariablesByInfoKeysResoponse> {
    const rpc = 'GetVariablesByInfoKeys';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=1056106)
   *
   * DM加载INFO数据
   */
  GetAllDataForDatamarket(
    req: info_service.GetAllDataForDatamarketRequest,
    options?: T,
  ): Promise<info_service.GetAllDataForDatamarketResponse> {
    const rpc = 'GetAllDataForDatamarket';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=1277548)
   *
   * 获取INFO引用详情信息
   */
  GetInfoReferenceDetail(
    req: info_service.GetInfoReferenceDetailRequest,
    options?: T,
  ): Promise<info_service.GetInfoReferenceDetailResp> {
    const rpc = 'GetInfoReferenceDetail';
    return this.request({ rpc, data: req }, options);
  }

  /** [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=1949139) */
  CronJob(
    req: info_service.CronJobRequest,
    options?: T,
  ): Promise<info_service.CronJobResponse> {
    const rpc = 'CronJob';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=3043883)
   *
   * 获取init&normal Info（分页）
   */
  GetInfoPage(
    req?: info_service.GetInfoPageRequest,
    options?: T,
  ): Promise<info_service.GetInfoPageResponse> {
    const rpc = 'GetInfoPage';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=3095423)
   *
   * info log debug
   */
  CheckDMLogMileStone(
    req: info_service.CheckDMLogMileStoneRequest,
    options?: T,
  ): Promise<info_service.CheckDMLogMileStoneResponse> {
    const rpc = 'CheckDMLogMileStone';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=3095450)
   *
   * info调试页面根据logID获取入参数据
   */
  GetInfoTestDataFromLogID(
    req: info_service.GetInfoTestDataFromLogIDRequest,
    options?: T,
  ): Promise<info_service.GetInfoTestDataResponse> {
    const rpc = 'GetInfoTestDataFromLogID';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=3095451)
   *
   * info调试页面获取初始化测试数据
   */
  GetInfoInitTestData(
    req: info_service.GetInfoInitTestDataRequest,
    options?: T,
  ): Promise<info_service.GetInfoInitTestDataResponse> {
    const rpc = 'GetInfoInitTestData';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=3130994)
   *
   * 批量更新info（字段部分更新）
   */
  BatchUpdateInfo(
    req?: info_service.BatchUpdateInfoRequest,
    options?: T,
  ): Promise<info_service.BatchUpdateInfoResponse> {
    const rpc = 'BatchUpdateInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.info_service/api_doc/show_doc?version=1.0.125&endpoint_id=3182309)
   *
   * info log v2
   */
  CheckDMLogMileStoneV2(
    req: info_service.CheckDMLogMileStoneV2Request,
    options?: T,
  ): Promise<info_service.CheckDMLogMileStoneV2Response> {
    const rpc = 'CheckDMLogMileStoneV2';
    return this.request({ rpc, data: req }, options);
  }
}
/* eslint-enable */
