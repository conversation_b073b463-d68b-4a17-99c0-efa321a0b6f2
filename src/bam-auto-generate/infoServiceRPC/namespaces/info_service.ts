// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string;

export enum Application {
  ALL = 0,
  JIN_NANG = 1,
  JIU_QUAN = 2,
  /** 在国际化对应 智能-妙计(因子) */
  ZHI_NENG = 3,
  DIAN_SHANG_GONG_DAN_MAI_DIAN = 4,
  GONG_ZUO_TAI_PEI_ZHI = 5,
  AFTER_SALE_WORKBENCH = 6,
  TOOL_CONFIG_WORKBENCH = 7,
  PRO_ACTIVE_SERVICE = 8,
  HOT_SPOT_WORKBENCH = 9,
  TRACE_DATA = 10,
  FEATURE = 11,
  RULE_ENGINE = 12,
  CARD_FACTORY = 13,
  CONFIGURATION_CENTER = 14,
  GONG_DAN_TEMPLATE = 15,
  CUSTOM_FIELDS = 16,
  TOOL_PREFILL_TAB = 17,
  JINNANG_ARBITRATION_ISSUE = 18,
  J<PERSON>NANG_SECOND_LINE_ISSUE = 19,
  ROBOT_KNOWLEDGE_BASE = 20,
  IM_ROUTE_FEATURE = 21,
  IVR_ROUTE_FEATURE = 22,
  HI_AGENT = 23,
  JINNANG_HIGH_PRIORITY_ISSUE = 24,
  /** L3占位符 */
  L3_JUDGEMENT_VARIABLE = 25,
  /** L3因子 */
  L3_TEXT_VARIABLE = 26,
  /** 在国际化对应 智能-妙计(占位符) */
  ZHI_NENG_MIAO_JI_PLACEHOLDER = 100,
  /** ola应用 */
  OLA_PLACEHOLDER = 133,
}

export enum ErrorParty {
  /** 上游调用方 */
  UPSTREAM = 0,
  /** DataMarket（DM）平台 */
  DATAMARKET = 1,
  /** DM 脚本处理 */
  DMSCRIPT = 2,
  /** 下游 RPC 服务 */
  DOWNSTREAMRPC = 3,
}

/** 启用/禁用 */
export enum OperateType {
  /** 禁用 */
  CLOSE = 0,
  /** 启用 */
  OPEN = 1,
}

/** InfoManage相关接口 */
export enum PermissionPoint {
  /** 研发 */
  Development = 1,
  /** 产品 */
  Product = 2,
  /** 坐席 */
  Operate = 3,
}

/** Info锦囊配置台相关接口 */
export enum Scene {
  SOP = 1,
  TOOL = 2,
  ZHI_NENG_TOOL_ROBOT = 3,
}

/** 获取/操作资源对象 */
export enum Target {
  Api = 1,
  Info = 2,
}

export enum Type {
  TOOL = 1,
  ACTION = 2,
  QUERY = 3,
  INIT = 4,
}

export interface Action {
  /** id */
  id: Int64;
  /** key */
  key?: string;
  /** 名称 */
  name: string;
  /** 不与上述type同义，model类型   rpc、api、app */
  type?: number;
  /** rpc对应psm，api对应IP+port+path */
  target?: string;
  /** rpc对应方法，api对应POST/GET 此处 Request入参为input时，包含GET+Query  output时为其他 */
  func?: string;
  /** 初始化入参列表,json串，包含展示名称,是否必选，默认值等 */
  initParams?: string;
  /** 描述，辅助选择 */
  desc?: string;
  /** 查询前缀 */
  queryPrefix?: string;
  /** 初始入参格式 */
  initFormat?: string;
  /** 多语言参数map */
  starlingMap?: string;
  /** 动作类型 1-通用动作 2-自定义动作 */
  actionType?: number;
  /** psm */
  psm?: string;
  /** method */
  method?: string;
  aliasName?: string;
  applications?: string;
  isAction?: number;
  grayPriority?: number;
  operationType?: number;
}

export interface ActionQuery {
  /** 入参对应的格式，数据中心场景下用于variables组装 */
  initFormat?: string;
  /** 查询前缀 */
  queryPrefix?: string;
  /** 输出info */
  outputList?: Array<Info>;
}

export interface Api {
  id?: Int64;
  apiKey?: string;
  name?: string;
  /** model类型 1工具 2动作 3查询 4系统 */
  type?: number;
  target?: string;
  func?: string;
  initParams?: string;
  initFormat?: string;
  queryPrefix?: string;
  scene?: string;
  apiDesc?: string;
  psm?: string;
  method?: string;
  aliasName?: string;
  grayPriority?: number;
  operationType?: number;
  actionType?: number;
  /** 应用渠道 */
  applicationList?: Array<Int64>;
}

export interface ApplicationReference {
  applicationId?: string;
  applicationName?: string;
  /** 展示的字段列表 */
  shownFieldList?: Array<ReferenceShownField>;
  referenceDetailList?: Array<ReferenceDetail>;
}

export interface AssembleFailRecord {
  code: string;
  reason: string;
  desc?: string;
  infoList?: Array<Info>;
}

export interface BatchGetInfoRequest {
  /** key列表 */
  keyList: Array<string>;
}

export interface BatchUpdateInfoRequest {
  /** key=info id */
  InfoUpdateDtoMap?: Record<Int64, InfoUpdateDto>;
}

export interface BatchUpdateInfoResponse {
  SuccessInfoIdList?: Array<Int64>;
  FailInfoIdList?: Array<Int64>;
}

export interface CheckDMLogMileStoneRequest {
  /** 关联本次调用的日志 ID */
  logId: string;
  /** 上游 PSM 标识 */
  upstreamPsm?: string;
  /** 目标生效机房，如 ROW/GCP/TTP */
  dataCenter: string;
}

export interface CheckDMLogMileStoneResponse {
  /** 每个责任方的错误详情 */
  errorList?: Partial<Record<ErrorParty, ErrorInfo>>;
  /** 每个 Infokey 的查询返回 */
  queryResults?: Array<QueryElement>;
  logFindSuccess?: boolean;
  infoLogTip?: string;
  logUrl?: string;
}

export interface CheckDMLogMileStoneV2Request {
  /** 关联本次调用的日志 ID */
  logId: string;
  /** 上游 PSM 标识 */
  upstreamPsm?: string;
  /** 目标生效机房，如 ROW/GCP/TTP */
  dataCenter: string;
}

export interface CheckDMLogMileStoneV2Response {
  /** 每一个request的详细信息 */
  requestDetails?: Record<string, RequestDetail>;
  /** 是否找到日志 */
  logFindSuccess?: boolean;
  /** 信息提示 */
  infoLogTip?: string;
  /** 日志链接 */
  logUrl?: string;
}

export interface CheckFailedRecord {
  /** 校验错误字段 */
  failedKey?: string;
  /** 校验错误原因 */
  failedDesc?: string;
}

export interface ConfigData {
  /** 配置信息类型，若value为数字枚举类型则为 numberEnum，若value为字符串枚举类型则为 stringEnum，其余为other */
  type: string;
  /** 类型为枚举类型时，此字段为空 */
  otherTypeConfigData?: string;
  /** 类型不为数字枚举类型时，此字段为空 */
  numberEnumConfigData?: Array<NumberEnumConfig>;
  /** 类型不为字符枚举类型时，此字段为空 */
  stringEnumConfigData?: Array<StringEnumConfig>;
  BaseResp?: base.BaseResp;
}

export interface CronJobRequest {
  cronJobKey: string;
}

export interface CronJobResponse {}

export interface Data {
  key?: string;
  value?: string;
  type?: string;
  desc?: string;
  descOptions?: DescOptions;
  options?: Array<OptionItem>;
  color?: string;
  enumCode?: number;
}

export interface DeleteApiRequest {
  /** apiId */
  apiId?: Int64;
  /** 操作流转邮箱,操作人姓名 */
  operateUserMail: string;
}

export interface DeleteApiResponse {}

export interface DeleteInfoRequest {
  /** infoId */
  infoId?: Int64;
  /** 操作流转邮箱,操作人姓名 */
  operateUserMail: string;
}

export interface DeleteInfoResponse {}

export interface DescOptions {
  contentTitle?: string;
  contentDesc?: string;
  data?: Array<Array<string>>;
}

export interface DisableOrEnableInfoOrApiRequest {
  /** 操作对象 */
  target: Target;
  /** 操作对象id */
  id: Int64;
  /** 操作类型 */
  operateType: OperateType;
  /** 操作人 */
  operateUserName?: string;
  /** 权限点 */
  permissionPoint?: PermissionPoint;
}

export interface DisableOrEnableInfoOrApiResponse {
  /** 操作结果 */
  result?: boolean;
  /** 操作结果信息 */
  message?: string;
}

export interface ErrorInfo {
  /** 至灰 = 0 成功 = 1 失败 = 2 */
  status?: number;
  /** 错误摘要列表，若只有日志则留空，k: spanId, v: 具体的error */
  errorDetails?: Record<string, Array<string>>;
  /** 原始日志条目 */
  logs?: Array<LogEntry>;
  /** 错误信息 */
  errorDetailList?: Array<string>;
}

export interface FeatureBusinessLineScene {
  label?: string;
  value?: number;
  children?: Array<FeatureBusinessLineScene>;
}

export interface GetActionRequest {
  /** 场景 */
  scene?: Scene;
  /** 类型 */
  type: Type;
  /** 接入方ID，可选，与新工单接入方定义相同 */
  accessPartyId?: number;
  isAction?: boolean;
  /** 是否需要关联的INFO信息 */
  needRelationInfo?: boolean;
  /** 接入方ID列表 */
  accessPartyIdList?: Array<number>;
  /** 应用场景列表 */
  applicationList?: Array<number>;
}

export interface GetActionResponse {
  /** action列表 */
  actionList?: Array<Action>;
  /** key:actionId,value:Info对象 */
  relationInfoMap?: Record<Int64, Array<Info>>;
}

export interface GetAllDataForDatamarketRequest {
  page: number;
  pageSize: number;
}

export interface GetAllDataForDatamarketResponse {
  /** INFO列表 */
  infoList?: Array<Info>;
  /** API列表 */
  apiList?: Array<Action>;
  /** 总数 */
  totalCount: number;
}

export interface GetApiDataListRequest {
  /** 筛选项 */
  selectMap: Record<string, string>;
  /** 权限点 */
  permissionPoint?: PermissionPoint;
  /** 页数 */
  pageNum: number;
  /** 页数大小 */
  pageSize: number;
}

export interface GetApiDataListResponse {
  /** 列表数据 */
  apiDataList: Array<Array<Data>>;
  /** api数据总量 */
  total: Int64;
}

export interface GetApiRequest {
  /** 场景 */
  scene?: Scene;
  /** 租户ID，字节为1 */
  tenantId?: Int64;
  /** 接入方ID，可选，与新工单接入方定义相同 */
  accessPartyId?: number;
  /** 类型 */
  type?: Type;
  /** 模糊搜索  名称 */
  potentialName?: string;
  apiId?: Int64;
  apiKey?: string;
  needRelationInfo?: boolean;
}

export interface GetApiResponse {
  /** api列表 */
  apiList?: Array<Api>;
  /** key:apiId,value:Info对象 */
  relationInfoMap?: Record<Int64, Array<Info>>;
}

export interface GetApiSelectPageSchemeRequest {
  /** 权限点 */
  permissionPoint?: PermissionPoint;
}

export interface GetApiSelectPageSchemeResponse {
  /** Api筛选项scheme */
  selectScheme: string;
  /** Api列表页scheme */
  pageScheme: string;
}

export interface GetConfigRequest {
  keyList: Array<string>;
}

export interface GetConfigResponse {
  /** TCC配置获取 */
  configDataList?: Array<ConfigData>;
}

export interface GetFeatureBusinessLineAndSceneRequest {}

export interface GetFeatureBusinessLineAndSceneResponse {
  featureBusinessLineScene?: Array<FeatureBusinessLineScene>;
}

export interface GetGqlLinkRequest {
  /** 测试数据 */
  newData: string;
}

export interface GetGqlLinkResponse {
  /** Gql链接 */
  gqlLink?: string;
}

export interface GetGraphQLDebugLinkRequest {
  /** infoId */
  infoId?: Int64;
}

export interface GetGraphQLDebugLinkResponse {
  debugLink?: string;
  assembleSuccess?: boolean;
  assembleFailReason?: string;
}

export interface GetInfoConfigCheckRequest {
  /** infokey */
  infoKey: string;
  infoIdList?: Array<Int64>;
  needCheckAccessParty?: boolean;
  accessPartyIdList?: Array<number>;
}

export interface GetInfoConfigCheckResponse {
  /** 校验状态-success */
  status: string;
  failReasonList?: Array<string>;
}

export interface GetInfoDataListRequest {
  /** 筛选项 */
  selectMap: Record<string, string>;
  /** 权限点 */
  permissionPoint?: PermissionPoint;
  /** 页数 */
  pageNum: number;
  /** 页数大小 */
  pageSize: number;
}

export interface GetInfoDataListResponse {
  /** 列表数据 */
  infoDataList: Array<Array<Data>>;
  /** info数据总量 */
  total: Int64;
}

export interface GetInfoInitParamsRequest {
  InfoKeyList: Array<string>;
  infoIdList?: Array<Int64>;
}

export interface GetInfoInitParamsResponse {
  AllInitParamsList: Array<InitParam>;
  /** key:infokey */
  InitParamDetailMap: Record<string, Array<InitParam>>;
}

export interface GetInfoInitTestDataRequest {
  apiId: string;
  infoKey: string;
  operateUserEmail: string;
  newData?: string;
}

export interface GetInfoInitTestDataResponse {
  initFormat?: string;
  infoVariables?: Array<InfoField>;
  /** 测试数据是否获取成功 */
  infoTestDataSuccess?: boolean;
  /** 测试数据未获取完整提示信息 */
  infoTestDataTip?: string;
}

export interface GetInfoPageRequest {
  /** 场景 */
  scene?: Scene;
  /** 租户ID，字节为1 */
  tenantId?: Int64;
  /** 接入方ID，可选，与新工单接入方定义相同 */
  accessPartyId?: number;
  /** info所属类型 */
  actionType?: Type;
  /** actionId */
  actionId?: Int64;
  /** 应用 */
  application?: Application;
  /** infoId列表 */
  infoIdList?: Array<Int64>;
  /** 枚举右值去重 */
  fieldOptionDistinct?: boolean;
  /** 接入方列表（并集查询） */
  accessPartyIdList?: Array<number>;
  /** 是否需要初始参数 */
  needInitParam?: boolean;
  /** 特征场景 */
  featureSceneList?: Array<number>;
  /** 应用渠道列表（并集查询） */
  applicationList?: Array<number>;
  /** 只要工作台维度带入的初始参数 */
  onlyInitParam?: boolean;
  /** 查询的字段 */
  queryFields?: Array<string>;
  /** 模糊查询字段，包括info key、info name、info desc */
  keyword?: string;
  /** 过滤掉初始info，即以INIT. 开头的INFO */
  filterOutInitInfo?: boolean;
  page?: number;
  pageSize?: number;
}

export interface GetInfoPageResponse {
  /** info列表 */
  infoList?: Array<Info>;
  AllInitParamsList?: Array<InitParam>;
  /** key:infokey */
  InitParamDetailMap?: Record<string, Array<InitParam>>;
  total?: number;
}

export interface GetInfoReferenceDetailRequest {
  infoId: string;
  applicationId: string;
}

export interface GetInfoReferenceDetailResp {
  applicationReference?: ApplicationReference;
}

export interface GetInfoRequest {
  /** 场景 */
  scene?: Scene;
  /** 租户ID，字节为1 */
  tenantId?: Int64;
  /** 接入方ID，可选，与新工单接入方定义相同 */
  accessPartyId?: number;
  /** info所属类型 */
  actionType?: Type;
  /** actionId */
  actionId?: Int64;
  /** 应用 */
  application?: Application;
  /** infoId列表 */
  infoIdList?: Array<Int64>;
  /** 枚举右值去重 */
  fieldOptionDistinct?: boolean;
  /** 接入方列表（并集查询） */
  accessPartyIdList?: Array<number>;
  /** 是否需要初始参数 */
  needInitParam?: boolean;
  /** 特征场景 */
  featureSceneList?: Array<number>;
  /** 应用渠道列表（并集查询） */
  applicationList?: Array<number>;
  /** 只要工作台维度带入的初始参数 */
  onlyInitParam?: boolean;
  /** 查询的字段 */
  queryFields?: Array<string>;
  /** 过滤掉初始info，即以INIT. 开头的INFO */
  filterOutInitInfo?: boolean;
}

export interface GetInfoResponse {
  /** info列表 */
  infoList?: Array<Info>;
  AllInitParamsList?: Array<InitParam>;
  /** key:infokey */
  InitParamDetailMap?: Record<string, Array<InitParam>>;
}

export interface GetInfoSelectPageSchemeRequest {
  /** 权限点 */
  permissionPoint?: PermissionPoint;
}

export interface GetInfoSelectPageSchemeResponse {
  /** Info筛选项scheme */
  selectScheme: string;
  /** Info列表页scheme */
  pageScheme: string;
}

export interface GetInfoTestDataFromLogIDRequest {
  logId: string;
  infoKey: string;
  upstreamPsm?: string;
}

export interface GetInfoTestDataRequest {
  /** api ID必传 */
  newData?: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  checkLog?: boolean;
  startTime?: string;
  endTime?: string;
  /** 操作人 */
  operator?: Operator;
}

export interface GetInfoTestDataResponse {
  /** 测试数据map */
  infoTestData?: Record<string, string>;
  /** 测试数据是否获取成功 */
  infoTestDataSuccess?: boolean;
  /** 测试数据未获取完整提示信息 */
  infoTestDataTip?: string;
}

export interface GetInfoTestResultRequest {
  /** 测试数据 */
  newData: string;
  /** 操作人 */
  operator?: Operator;
}

export interface GetInfoTestResultResponse {
  success?: boolean;
  /** info测试数据结果 */
  infoTestResult?: string;
  /** 失败原因 */
  failReason?: Array<string>;
}

export interface GetInputFormatListRequest {
  psm: string;
  method: string;
}

export interface GetInputFormatListResponse {
  /** 入参格式string */
  inputFormatData?: string;
  /** 以旧的「入参格式」框 显示 */
  isNewInputFormat?: number;
}

export interface GetInsertOrUpdateSchemaRequest {
  permissionPoint: PermissionPoint;
  target: Target;
  dataId?: Int64;
}

export interface GetInsertOrUpdateSchemaResponse {
  schema?: string;
  starlingMap?: Record<string, string>;
  status?: number;
}

export interface GetOperateRecordListRequest {
  /** 查询目标 */
  target: Target;
  /** 查询目标Id */
  id: Int64;
  /** 查询时间start */
  stratTime?: Int64;
  /** 查询时间end */
  endTime?: Int64;
  /** 页面大小不传默认15 */
  pageSize?: number;
  /** 页码，不传默认为1 */
  pageNum?: number;
  /** 当前查询时间 */
  queryTime: Int64;
}

export interface GetOperateRecordListResponse {
  /** 操作记录 */
  operateRecordList?: Array<OperateRecord>;
  /** 总数 */
  totalCount?: number;
  /** 总页数 */
  pageCount?: number;
}

export interface GetQueryByActionRequest {
  /** actionId列表 */
  idList: Array<Int64>;
}

export interface GetQueryByActionResponse {
  /** actionQuery映射表 */
  actionQueryMap?: Record<Int64, ActionQuery>;
}

export interface GetQueryByInfoRequest {
  /** 需要的infoKey列表 */
  infoKeyList: Array<string>;
  /** 已有的infoKey列表 */
  hasInfoKeyList: Array<string>;
  /** 是否需要校验 */
  needCheck?: boolean;
  /** 是否需要拆分query */
  needSplit?: boolean;
}

export interface GetQueryByInfoResponse {
  /** 查询语句 */
  query?: string;
  /** 变量 */
  variable?: string;
  /** 是否组装成功 */
  success?: boolean;
  /** 组装失败原因列表 */
  assembleFailList?: Array<AssembleFailRecord>;
  /** 拆分的信息列表 */
  splitResult?: Array<QueryData>;
}

export interface GetUpdateDataRequest {
  target: Target;
  dataId?: Int64;
}

export interface GetUpdateDataResponse {
  /** 回显表单数据 */
  dataForUpdata?: string;
}

export interface GetVariablesByInfoKeysRequest {
  /** InfoKeys */
  infoKeys: Array<string>;
}

export interface GetVariablesByInfoKeysResoponse {
  variables: string;
}

export interface Info {
  /** id */
  id: Int64;
  /** api id */
  apiId: Int64;
  /** 带path，用于组装query+output标识 */
  key: string;
  /** 别名(一维,用于匹配api中的入参) */
  aliasKey?: string;
  /** 展示名称 */
  name?: string;
  /** 用户组件格式校验 string number boolean */
  type?: string;
  /** 用户组件子类型 */
  subType?: string;
  /** 1是 0否 */
  isList?: number;
  /** 名称全路径，用于搜索 */
  fullPath?: string;
  /** JSON 渲染字面需要，也包含支持的操作符 */
  fieldOption?: string;
  /** info描述,用于辅助运营理解 */
  desc?: string;
  /** 指令 */
  instruction?: string;
  /** info默认值 */
  defaultValue?: string;
  /** 多语言参数map */
  starlingMap?: string;
  /** 接入方列表 */
  accessPartyIds?: string;
  /** 更新人邮箱 */
  updateUserMail?: string;
  /** 脱敏类型字段权限范围 */
  desensitizationAuthScope?: string;
  /** 创建人邮箱 */
  createUserMail?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 更新人姓名 */
  updateUserName?: string;
  /** 查询类型 1-通用查询 2-自定义查询 */
  queryType?: number;
  /** 特征业务线 */
  featureBusinessLine?: Array<number>;
  /** 特征场景 */
  featureScene?: Array<number>;
  /** 应用渠道列表 */
  applicationList?: Array<Int64>;
}

export interface InfoField {
  field?: string;
  fieldName?: string;
  fieldDesc?: string;
  /** 配置的测试数据 */
  fieldTestVal?: string;
  /** 离线+日志获取的测试数据 */
  fieldOfflineTestVal?: string;
  fieldType?: string;
  fieldIsRequired?: boolean;
}

export interface InfoKeyResult {
  /** 原始的Infokey */
  InfoKey: string;
  /** 对应的值 */
  InfoValue: string;
  /** 值来源，可映射Value来源 */
  ValueSource: number;
}

export interface InfoUpdateDto {
  Name?: string;
  Desc?: string;
  Instruction?: string;
  DefaultValue?: string;
  StarlingMap?: string;
  UpdateUserMail?: string;
  UpdateUserName?: string;
  AccessPartyId?: number;
  QueryType?: number;
  ActionType?: number;
  FeatureBusinessLine?: number;
  FeatureScene?: number;
}

export interface InitParam {
  label: string;
  value: string;
  needFormCheck?: boolean;
  /** staticEnum-静态枚举参数，dynamicEnum-动态枚举参数,dynamicCascader-及联枚举参数,normal-非自定义查询 */
  type?: string;
  valueInfoId?: Int64;
  labelInfoId?: Int64;
  fieldOption?: string;
  isMultiple?: boolean;
  cascaderInfoId?: Int64;
}

export interface LogEntry {
  /** 日志消息 */
  logMsg?: string;
  /** Pod 名称 */
  pod?: string;
  /** 发生日志的节点 IP */
  ip?: string;
  spanId?: string;
  /** 机房 */
  timeStamp?: string;
  /** 集群 */
  idc?: string;
  /** PSM */
  psm?: string;
  /** Log */
  logId?: string;
  /** 文件位置 */
  location?: string;
  level?: string;
  ifPpe?: boolean;
}

export interface NumberEnumConfig {
  label: string;
  value: Int64;
}

export interface OperateRecord {
  /** id */
  id?: Int64;
  /** 操作内容 */
  operateDetail?: Array<string>;
  /** 操作时间 */
  operateTime?: string;
  /** 操作人 */
  operateUserMail?: string;
  /** 操作人姓名 */
  operateUserName?: string;
  operateDesc?: string;
}

export interface Operator {
  /** 操作人ID */
  userId: string;
  /** 操作人邮箱 */
  email: string;
  /** 操作人姓名 */
  userName?: string;
}

export interface OptionItem {
  title?: string;
  itemList?: Array<string>;
}

export interface QueryApiAndInfoKeyListWithRepeatOrWrongRequest {}

export interface QueryApiAndInfoKeyListWithRepeatOrWrongResponse {
  /** api和infoKey列表(重复函数) */
  apiAndInfoKeysRepeatMap?: Record<Int64, Array<string>>;
  /** api和infoKey列表（入参格式比初始入参变量多） */
  apiAndInfoKeysWrongMap?: Record<Int64, Array<string>>;
}

export interface QueryApiApplicationsAndAccessPartyRequest {
  apiId: Int64;
}

export interface QueryApiApplicationsAndAccessPartyResponse {
  /** 应用渠道 */
  applications: Array<StringEnumConfig>;
  /** 接入方 */
  accessPartys: Array<StringEnumConfig>;
}

export interface QueryApiPsmPrefixListRequest {
  /** psm搜索前缀 */
  psmPrefix: string;
}

export interface QueryApiPsmPrefixListResponse {
  /** Psm推荐列表list */
  psmPrefixList?: Array<string>;
}

export interface QueryData {
  infoKeys?: Array<string>;
  query?: string;
  variable?: string;
  pageLevel?: number;
}

export interface QueryElement {
  requestId?: string;
  requestParams?: Record<string, string>;
  requestInfokeys?: Array<string>;
  infoKeyResults?: Array<InfoKeyResult>;
  timeStamp?: string;
}

export interface QueryInfoKeyListRequest {
  apiId: Int64;
  infoKey: string;
}

export interface QueryInfoKeyListResponse {
  /** infoKey列表 */
  infoKeysList: Array<string>;
}

export interface QueryMethodListByPsmRequest {
  psmName: string;
}

export interface QueryMethodListByPsmResponse {
  /** Psm下Method列表list */
  methodList?: Array<string>;
}

export interface ReferenceDetail {
  accessPartyName?: string;
  entitySubName?: string;
  entityId?: string;
  entityName?: string;
  statusNameList?: Array<string>;
  jumpLinkPath?: string;
  accessPartyId?: string;
}

export interface ReferenceShownField {
  fieldKey?: string;
  fieldName?: string;
  /** 跳转链接-JumpUrl */
  canJump?: boolean;
  isList?: boolean;
  jumpUrlPattern?: string;
}

export interface RequestDetail {
  /** 每一个request的错误详情 */
  errorList?: Partial<Record<ErrorParty, ErrorInfo>>;
  /** 每一个request的参数 */
  requestParams?: Record<string, string>;
  /** 每一个request的infoKey */
  requestInfokeys?: Array<string>;
  /** 每一个request的infoKey结果 */
  infoKeyResults?: Array<InfoKeyResult>;
  /** 每一个request的时间戳 */
  timeStamp?: string;
  /** 每一个request的调用方psm */
  callerPsm?: string;
}

export interface SaveApiRequest {
  /** 数据 */
  data: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  /** apiId 没有Id代表新建 */
  apiId?: Int64;
  /** 权限 */
  permissionPoint: PermissionPoint;
}

export interface SaveApiResponse {
  apiId: Int64;
}

export interface SaveInfoRequest {
  /** 数据 */
  data: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  /** infoId 没有Id代表新建 */
  infoId?: Int64;
  /** 权限 */
  permissionPoint: PermissionPoint;
}

export interface SaveInfoResponse {
  infoId: Int64;
}

export interface SearchInfoRequest {
  /** 场景 */
  scene?: Scene;
  /** 租户ID，字节为1 */
  tenantId?: Int64;
  /** 接入方ID，可选，与新工单接入方定义相同 */
  accessPartyId?: number;
  /** 搜索关键字 */
  keyword: string;
}

export interface SearchTransferUserRequest {
  /** 搜索内容 */
  searchContent: string;
}

export interface SearchTransferUserResponse {
  /** 搜索流转人列表 */
  transferUserList: Array<TransferUser>;
}

export interface StringEnumConfig {
  label: string;
  value: string;
}

export interface TransferInfoRequest {
  /** infoId 没有Id代表新建 */
  infoId: Int64;
  /** 操作流转邮箱 */
  operateTransferUserMail: string;
  /** 流转人邮箱 */
  transferToUserMail: string;
}

export interface TransferInfoResponse {}

export interface TransferUser {
  email: string;
  name: string;
}

export interface UpdateOrAddDataRequest {
  /** 目标 */
  target: Target;
  /** 新数据 */
  newData: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  /** dataId */
  dataId?: Int64;
  permissionPoint: PermissionPoint;
}

export interface UpdateOrAddDataResponse {
  failRecordList?: Array<CheckFailedRecord>;
}
/* eslint-enable */
