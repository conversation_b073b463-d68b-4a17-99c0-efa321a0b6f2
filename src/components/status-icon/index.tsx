import {
  IconAlertTriangle,
  IconTickCircle,
  IconUploadError,
} from '@hi-design/ui-icons';
import styles from './index.module.scss';
import { FC } from 'react';
import { Status } from '@/common/constants/info';

export const compareStatus = (a: { status: Status }, b: { status: Status }) => {
  const priority = {
    [Status.Error]: 0,
    [Status.Warning]: 1,
    [Status.Success]: 2,
  };

  return priority[a.status] - priority[b.status];
};

export interface StatusIconProps {
  status: Status;
}

export const StatusIcon: FC<StatusIconProps> = ({ status }) => {
  if (status === Status.Success) {
    return (
      <IconTickCircle className={`${styles.statusIcon} ${styles.success}`} />
    );
  }
  if (status === Status.Warning) {
    return (
      <IconAlertTriangle className={`${styles.statusIcon} ${styles.warning}`} />
    );
  }
  if (status === Status.Error) {
    return (
      <IconUploadError className={`${styles.statusIcon} ${styles.error}`} />
    );
  }
  return null;
};
