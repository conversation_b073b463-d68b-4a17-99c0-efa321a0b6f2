import { FC } from 'react';
import styles from './index.module.scss';
import { Typography } from '@hi-design/ui';
import { NoData } from '../no-data';

export interface ILogRequestDataProps {
  data?: {
    title: string;
    value: string;
    key: string;
  }[];
  className?: string;
}
export const RequestParams: FC<ILogRequestDataProps> = props => {
  const { data, className } = props;
  return (
    <div
      className={`${className || ''} ${styles.container} ${data?.length ? '' : styles.empty}`}
    >
      {data?.length ? (
        data?.map(item => (
          <div className={styles.item} key={item.key}>
            <Typography.Text
              type="secondary"
              className={styles.title}
              ellipsis={{ showTooltip: true }}
            >
              {item.title}
            </Typography.Text>
            <Typography.Text
              ellipsis={{ showTooltip: true }}
              strong
              className={styles.value}
            >
              {item.value}
            </Typography.Text>
          </div>
        ))
      ) : (
        <NoData />
      )}
    </div>
  );
};
