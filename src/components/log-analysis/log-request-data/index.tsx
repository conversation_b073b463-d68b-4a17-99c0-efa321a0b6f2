import { Typography } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
import { RequestParams } from '../request-params';
import { InfoValueTable } from '../info-value-table';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
export interface LogRequest {
  key: string;
  tabDesc: string;
  requestParams: {
    title: string;
    value: string;
  }[];
  infoKeyResults: info_service.InfoKeyResult[];
  infoKeyDiff: string[];
}

export interface ILogRequestDataProps {
  // requestData: LogRequest[];
  requestParams: {
    title: string;
    value: string;
  }[];
  infoKeyResults: info_service.InfoKeyResult[];
  infoKeyDiff: string[];
  activeInfoKey?: string;
}

export const LogRequestData: FC<ILogRequestDataProps> = props => {
  const { requestParams, infoKeyDiff, infoKeyResults, activeInfoKey } = props;

  return (
    <div className={styles.container}>
      <div>
        <Typography.Text strong className={styles.header}>
          {INFO_DEBUG_TEXT_MAP.REQUEST_PARAMS}
        </Typography.Text>
        <RequestParams
          data={requestParams?.map(item => ({
            ...item,
            key: item.title,
          }))}
          className={styles.descriptions}
        />
      </div>
      <div>
        <Typography.Text strong className={styles.header}>
          {INFO_DEBUG_TEXT_MAP.INFO_DATA_RETURN}
        </Typography.Text>
        <InfoValueTable
          infoKeyDiff={infoKeyDiff}
          infoKeyResults={infoKeyResults}
          activeInfoKey={activeInfoKey}
        />
      </div>
    </div>
  );
};
