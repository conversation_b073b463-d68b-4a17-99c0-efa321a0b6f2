import { FC } from 'react';

import { Select, Space, Tooltip } from '@hi-design/ui';
import { Icon<PERSON><PERSON>, IconServer } from '@hi-design/ui-icons';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import styles from './index.module.scss';

export interface TabExtraProps {
  selectedInfoKey: string;
  selectedPSM: string;
  allInfoKeys: string[];
  allPSMs: string[];
  handleSelectInfoKey: (value: string) => void;
  handleSelectPSM: (value: string) => void;
}

export const TabExtra: FC<TabExtraProps> = props => {
  const {
    selectedInfoKey,
    selectedPSM,
    allInfoKeys,
    allPSMs,
    handleSelectInfoKey,
    handleSelectPSM,
  } = props;

  return (
    <Space className={styles.tabExtraContainer}>
      <Select
        showClear
        filter
        prefix={
          <Tooltip content={INFO_DEBUG_TEXT_MAP.SELECT_INFO_KEY_TIP}>
            <IconKey className={styles.selectIcon} />
          </Tooltip>
        }
        value={selectedInfoKey}
        placeholder={INFO_DEBUG_TEXT_MAP.SELECT_INFO_KEY_PLACEHOLDER}
        onChange={value => {
          handleSelectInfoKey(value as string);
        }}
        optionList={allInfoKeys.map(key => ({
          label: key,
          value: key,
        }))}
        style={{ width: 250, marginRight: 10 }}
        dropdownStyle={{ maxWidth: 1000 }}
      />
      <Select
        showClear
        filter
        value={selectedPSM}
        placeholder={INFO_DEBUG_TEXT_MAP.SELECT_PSM_PLACEHOLDER}
        style={{ width: 150 }}
        prefix={
          <Tooltip content={INFO_DEBUG_TEXT_MAP.SELECT_PSM_TIP}>
            <IconServer className={styles.selectIcon} />
          </Tooltip>
        }
        optionList={allPSMs.map(psm => ({
          label: psm,
          value: psm,
        }))}
        onChange={value => {
          handleSelectPSM(value as string);
        }}
      />
    </Space>
  );
};
