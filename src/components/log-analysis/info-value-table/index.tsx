import { FC, useEffect, useMemo, useState } from 'react';
import styles from './index.module.scss';
import { List, Tooltip, Typography } from '@hi-design/ui';
import classNames from 'classnames';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { StatusIcon, compareStatus } from '@/components/status-icon';
import { Status } from '@/common/constants/info';

export interface IInfoValueTableProps {
  activeInfoKey?: string; // 当前页面携带的info key
  infoKeyResults: info_service.InfoKeyResult[];
  infoKeyDiff: string[];
}

export const InfoValueTable: FC<IInfoValueTableProps> = props => {
  const { activeInfoKey, infoKeyResults, infoKeyDiff } = props;
  const [selectedInfoKey, setSelectedInfoKey] = useState<string>(
    activeInfoKey || '',
  );

  useEffect(() => {
    if (!activeInfoKey) {
      return;
    }
    setSelectedInfoKey(activeInfoKey);
  }, [activeInfoKey]);

  const infoValues = useMemo(() => {
    const res = infoKeyDiff?.map(key => ({
      infoKey: key,
      infoValue: '',
      status: Status.Error,
    }));
    const formatInfoKeyResults = infoKeyResults?.map(item => ({
      infoKey: item.InfoKey,
      infoValue: item.InfoValue,
      status: item.InfoValue ? Status.Success : Status.Warning,
    }));
    return res.concat(formatInfoKeyResults).sort((a, b) => {
      if (a.status !== b.status) {
        return compareStatus(a, b);
      }
      return a.infoKey.localeCompare(b.infoKey);
    });
  }, [infoKeyDiff, infoKeyResults]);

  const getToolTipContent = (status: Status) => {
    if (status === Status.Success) {
      return INFO_DEBUG_TEXT_MAP.RESPONSE_SUCCESS_WITH_VALUE;
    }
    if (status === Status.Warning) {
      return INFO_DEBUG_TEXT_MAP.RESPONSE_SUCCESS_NO_VALUE;
    }
    if (status === Status.Error) {
      return INFO_DEBUG_TEXT_MAP.RESPONSE_NOT_RETURNED;
    }
    return null;
  };

  const selectedInfoKeyValue = useMemo(() => {
    if (!selectedInfoKey) {
      return INFO_DEBUG_TEXT_MAP.SELECT_INFO_KEY_TO_VIEW_VALUE;
    }
    const selectedItem = infoValues
      .filter(val => val.infoKey === selectedInfoKey)
      .at(0);

    if (!selectedItem) {
      return INFO_DEBUG_TEXT_MAP.INFO_KEY_NOT_FOUND(selectedInfoKey);
    }

    return (
      infoValues.filter(val => val.infoKey === selectedInfoKey).at(0)
        ?.infoValue || INFO_DEBUG_TEXT_MAP.NO_VALUE_RETURNED
    );
  }, [selectedInfoKey, infoValues]);

  const handleTableItemClick = (
    e: React.MouseEvent<HTMLLIElement>,
    infoKey: string,
  ) => {
    e.stopPropagation();
    setSelectedInfoKey(infoKey);
  };

  const renderItem = (item: {
    infoKey: string;
    infoValue: string;
    status: string;
  }) => (
    <List.Item
      key={item.infoKey}
      className={classNames(styles.infoKeyItem, {
        [styles.highlight]: item.infoKey === activeInfoKey,
        [styles.active]: item.infoKey === selectedInfoKey,
      })}
      onClick={e => {
        handleTableItemClick(e, item.infoKey);
      }}
    >
      <Tooltip content={getToolTipContent(item.status as Status)}>
        <StatusIcon status={item.status as Status} />
      </Tooltip>
      <Typography.Text
        className={styles.infoKeyText}
        ellipsis={{ showTooltip: true, pos: 'middle' }}
        type={selectedInfoKey === item.infoKey ? 'primary' : 'secondary'}
      >
        {item.infoKey}
      </Typography.Text>
    </List.Item>
  );

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <Typography.Text strong className={styles.header}>
          {INFO_DEBUG_TEXT_MAP.INFO_KEYS}
        </Typography.Text>
        <List
          className={styles.infoKeyList}
          size="small"
          bordered
          dataSource={infoValues}
          renderItem={renderItem}
        />
      </div>

      <div className={styles.main}>
        <Typography.Text strong className={styles.header}>
          {INFO_DEBUG_TEXT_MAP.RESPONSE_DATA}
        </Typography.Text>
        <div className={styles.infoValue}>
          <Typography.Paragraph>{selectedInfoKeyValue}</Typography.Paragraph>
        </div>
      </div>
    </div>
  );
};
