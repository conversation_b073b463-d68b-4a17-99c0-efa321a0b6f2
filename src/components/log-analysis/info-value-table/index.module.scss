.container {
  display: flex;
  gap: 12px;

  .main {
    width: 50%;

    .header {
      display: block;
      padding: 8px 12px;
      border: 1px solid var(--semi-color-border);
    }
  }

  .infoKeyList {
    width: 100%;
    height: 185px;
    overflow-y: auto;

    .infoKeyItem {
      &:hover {
        color: var(--semi-color-text-0);
        background-color: rgba(var(--semi-grey-0), 1);
        cursor: pointer;
      }

      &.active {
        color: var(--semi-color-text-0);
        font-weight: 600;
        background-color: rgba(var(--semi-grey-0), 1);
      }

      &.highlight {
        background-color: rgba(var(--semi-yellow-4), 0.5);
      }

      .statusIcon {
        margin-right: 8px;

        &.success {
          color: var(--semi-color-success);
        }

        &.warning {
          color: var(--semi-color-warning);
        }

        &.error {
          color: var(--semi-color-danger);
        }
      }

      .infoKeyText {
        margin-left: 4px;
        width: calc(100% - 24px);
      }
    }
  }

  .infoValue {
    width: 100%;
    padding: 12px;
    height: 185px;
    overflow-y: auto;
    background-color: var(--semi-color-fill-0);
  }
}
