import { ExecResult } from '@/types/info-debug';
import { Descriptions, Space, Spin, Tooltip, Typography } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';
import { JsonDisplay } from '../test-request-params/json-display';
import { StatusIcon } from '@/components/status-icon';
import { Status } from '@/common/constants/info';

export interface ExecutionResultProps {
  execResult: ExecResult | null;
  isResultLoading: boolean;
  handleClickLogId: (logId: string) => void;
}

export const ExecutionResult: FC<ExecutionResultProps> = props => {
  const { execResult, isResultLoading, handleClickLogId } = props;

  const data = [
    {
      key: INFO_DEBUG_TEXT_MAP.RUN_RESULT,
      value: execResult?.data || INFO_DEBUG_TEXT_MAP.NO_DATA_RETURN,
    },
    {
      key: 'Log ID',
      value: (
        <Tooltip content={INFO_DEBUG_TEXT_MAP.JUMP_TO_LOG_ANALYSIS}>
          <Typography.Text
            link
            onClick={() => handleClickLogId(execResult?.logId || '')}
          >
            {execResult?.logId || '-'}
          </Typography.Text>
        </Tooltip>
      ),
    },
    execResult?.status === Status.Error && {
      key: INFO_DEBUG_TEXT_MAP.ERROR_DETAILS,
      value: (
        <Typography.Text
          ellipsis={{
            rows: 3,
            expandable: true,
            collapsible: true,
            collapseText: COMMON_FE_TEXT_MAP.Collapse,
            expandText: COMMON_FE_TEXT_MAP.Expand,
          }}
        >
          {execResult?.errorMsg.join('\n') || '-'}
        </Typography.Text>
      ),
    },
    {
      key: INFO_DEBUG_TEXT_MAP.RAW_DATA_RETURN,
      value: (
        <JsonDisplay
          maxHeight={200}
          copiable={{}}
          jsonData={execResult?.rawResp || ''}
        />
      ),
    },
  ].filter(item => !!item);

  return (
    <Spin spinning={isResultLoading} wrapperClassName={styles.spinWrapper}>
      <div className={styles.spinContainer}>
        {execResult ? (
          <div>
            <Space className={styles.title}>
              <StatusIcon status={execResult?.status || Status.Error} />
              <Typography.Text strong>
                {execResult?.status === Status.Success
                  ? INFO_DEBUG_TEXT_MAP.DATA_TEST_SUCCESS
                  : INFO_DEBUG_TEXT_MAP.DATA_TEST_FAIL}
              </Typography.Text>
            </Space>
            <Descriptions
              align="center"
              layoutInfo={{
                titleWidth: 100,
              }}
              className={styles.descriptionContent}
              data={data}
            />
          </div>
        ) : (
          <Typography.Text type="tertiary" className={styles.emptyText}>
            {INFO_DEBUG_TEXT_MAP.NOT_RUN_YET}
          </Typography.Text>
        )}
      </div>
    </Spin>
  );
};
