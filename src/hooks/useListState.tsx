/* eslint-disable max-lines-per-function */
import transferValue from '@common/utils/transferValue';
import {
  Data,
  infoServiceClient,
  OperateType,
  PermissionPoint,
  Target,
} from '@http_idl/infoService';
import {
  API_FE_TEXT_MAP,
  COMMON_FE_TEXT_MAP,
  INFO_FE_TEXT_MAP,
} from '@common/constants/I18nTextMap';
import { useHistory } from '@edenx/runtime/router-v5';
import { DisableOrAbleWords, StatusType } from '@common/constants/listConfig';
import { useEffect, useRef, useState } from 'react';
import {
  Modal,
  Toast,
  Dropdown,
  Button,
  Typography,
  Space,
} from '@hi-design/ui';
import { Agent } from '@ies/unified_communications_sdk';
import transferFilterData from '@common/utils/transferFilterData';
import { PAGE_MARGIN } from '@common/constants/index';
import { isNil } from 'lodash-es';
import {
  sendCustomPerfMetric,
  slardarTimeLogger,
  SlardarEventName,
} from '@sdks/slardar';
import { getCurrentTime } from '@common/utils/time';
import { getDefaultChannel } from '@utils/url';
import { IconMore } from '@hi-design/ui-icons';
import { INFO_DEBUG_TAB } from '@/common/constants/info';
import { RouterPath } from '@/const';
import { infoServiceRpcService } from '@/common/http/infoServiceRPCService';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';

interface IJumpDetail {
  id?: string;
}
export interface IUseListState {
  target: Target;
  permissionPoint: PermissionPoint;
  agent: Agent | null;
  setPageLoading: (loading: boolean) => void;
}
export const useListState = (props: IUseListState) => {
  const { target, permissionPoint, agent, setPageLoading } = props || {};

  const defaultProps = {
    pageSize: 20, // 一页有几条数据
    pageNo: 1, // 分页-第几页
    filterData: {
      applications: getDefaultChannel(),
    }, // 筛选项数据
  };
  const history = useHistory();
  const [dataId, setDataId] = useState<string>(null);
  const [recordsVisible, setRecordsVisible] = useState<boolean>(false);
  // 列表schema
  const [pageSchema, setPageSchema] = useState('');
  // 筛选项schema
  const [selectSchema, setSelectSchema] = useState('');
  // 列表数据
  const [listData, setListData] = useState<Array<Array<Data>>>([]);
  // table loading
  const [tableLoading, setTableLoading] = useState(false);
  // 页面是否显示空页面
  const [isPageEmpty, setIsPageEmpty] = useState(false);

  const [tableWidth, setTableWidth] = useState(0);

  const dataProps = useRef(defaultProps);
  const isInitRef = useRef(true);

  const onRecordsCancel = (): void => {
    setRecordsVisible(false);
    setDataId(null);
  };
  const onRecordsShow = (id: string): void => {
    setRecordsVisible(true);
    setDataId(id);
  };

  /**
   * @description: 获取info列表数据
   */
  const getData = async (
    { pageNo: page = 1, pageSize: size = 20, filterData: filter = {} },
    isFirst = false,
  ): Promise<void> => {
    // 非首次执行这个方法才需要table里的loading，首次渲染需要页面的loading
    !isFirst && setTableLoading(true);
    const startTime = getCurrentTime();
    try {
      // 构建通用请求参数
      const baseParams = {
        selectMap: filter,
        permissionPoint,
        pageNum: page,
        pageSize: size,
      };

      // 根据target选择对应的API方法和参数
      const apiConfig = {
        [Target.Info]: {
          method: infoServiceRpcService.GetInfoDataList,
          params: {
            ...baseParams,
            lan: 'en',
          } satisfies info_service.GetInfoDataListRequest,
          dataKey: 'infoDataList',
        },
        [Target.Api]: {
          method: infoServiceRpcService.GetApiDataList,
          params: baseParams satisfies info_service.GetApiDataListRequest,
          dataKey: 'apiDataList',
        },
      };

      const config = apiConfig[target];
      if (!config) {
        throw new Error(`Unsupported target: ${target}`);
      }

      const result = await config.method(config.params);
      const { total = '0' } = result;
      const dataList =
        target === Target.Info
          ? (result as info_service.GetInfoDataListResponse)?.infoDataList || []
          : (result as info_service.GetApiDataListResponse)?.apiDataList || [];

      setListData(dataList as Array<Array<Data>>);
      setPagination({
        ...pagination,
        currentPage: page,
        total: Number(total),
        pageSize: size,
      });
      slardarTimeLogger({
        eventName: SlardarEventName.PROCESS_LIST_SEARCH,
        duration: getCurrentTime() - startTime,
        target,
      });
    } catch (err) {
      console.error('getData err:', err);
    }
    // 首次执行这个方法只需要页面级的loading，非首次执行只需要table里的loading
    isFirst ? setPageLoading(false) : setTableLoading(false);
  };

  /**
   * @description: 获得筛选项和列表的schema
   */
  const getSchema = async (): Promise<void> => {
    setPageLoading(true);
    try {
      let result = {} as any;
      if (target === Target.Info) {
        result = await infoServiceClient.GetInfoSelectPageScheme({
          permissionPoint,
        });
      }
      if (target === Target.Api) {
        result = await infoServiceClient.GetApiSelectPageScheme({
          permissionPoint,
        });
      }
      if (result?.BaseResp?.StatusCode !== 0 || !result?.pageScheme) {
        setPageLoading(false);
        setIsPageEmpty(true);
        return;
      }
      setPageSchema(result.pageScheme || '');
      setSelectSchema(result.selectScheme || '');
    } catch (err) {
      setPageLoading(false);
      setIsPageEmpty(true);
      console.error('infolist getSchema error: ', err);
    }
  };

  const fetchSchemaAndData = async (): Promise<void> => {
    await Promise.all([getSchema(), getData(defaultProps, true)]);
    if (isInitRef.current) {
      isInitRef.current = false;
      sendCustomPerfMetric({
        target,
        page: 'list',
      });
    }
  };

  const handleChangePagiation = (currentPage: number, size: number) => {
    dataProps.current.pageNo = currentPage;
    dataProps.current.pageSize = size;
    getData(dataProps.current);
  };

  // 分页
  const [pagination, setPagination] = useState<any>({
    pageSize: dataProps.current.pageSize,
    currentPage: dataProps.current.pageNo,
    total: 0,
    showSizeChanger: true,
    onChange: handleChangePagiation,
  });

  // =======================  页面操作部分
  /**
   * @description 点击 “删除”的二次弹窗确认按钮逻辑
   */
  const handleDelete = async (id: string, name: string): Promise<void> => {
    try {
      let result = {} as any;
      if (target === Target.Info) {
        result = await infoServiceClient.DeleteInfo({
          infoId: id,
          operateUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
        });
      }
      if (target === Target.Api) {
        result = await infoServiceClient.DeleteApi({
          apiId: id,
          operateUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
        });
      }
      const { BaseResp } = result;
      if (BaseResp?.StatusCode === 0) {
        await getData?.(dataProps.current);
        Toast.success(COMMON_FE_TEXT_MAP.Delete_Success_Name(name));
      } else {
        Toast.error(
          BaseResp?.StatusMessage || COMMON_FE_TEXT_MAP.Delete_Fail_Name(name),
        );
      }
    } catch (err) {
      console.error(err, 'delete info');
    }
  };

  const onJumpFormDetail = (data: IJumpDetail): void => {
    const { id } = data || {};
    const urlPrefix = target === Target.Info ? '/info/detail' : '/api/detail';
    const queryData = id ? `id=${id}` : '';
    // window.open(`${urlPrefix}?${queryData}`);
    history.push(`${urlPrefix}?${queryData}`);
  };

  const onJumpDebug = (data: IJumpDetail): void => {
    const { id } = data || {};
    if (target !== Target.Info) {
      return;
    }
    if (!id) {
      // 列表入口 目前不传id，只展示日志分析
      history.push(
        `${RouterPath.INFO_DEBUG}?tab=${INFO_DEBUG_TAB.LOG_ANALYSIS.key}`,
      );
      return;
    }
    history.push(`${RouterPath.INFO_DEBUG}?id=${id}`);
  };

  // @description 删除API弹窗
  const handleDeleteModal = (id, name) => {
    Modal.warning({
      title: COMMON_FE_TEXT_MAP.Delete_Name(name),
      okText: COMMON_FE_TEXT_MAP.Delete,
      cancelText: COMMON_FE_TEXT_MAP.Cancel,
      onOk: () => handleDelete(id, name),
    });
  };

  // @description: 点击“启用/禁用”的二次弹窗的确认按钮后的逻辑
  const handleModal = async (
    isAbleOrDisable: string,
    id: string,
    name: string,
  ): Promise<void> => {
    const type =
      isAbleOrDisable === DisableOrAbleWords.Disable
        ? OperateType.CLOSE
        : OperateType.OPEN;
    const ToastSuccess =
      isAbleOrDisable === DisableOrAbleWords.Disable
        ? COMMON_FE_TEXT_MAP.Disable
        : COMMON_FE_TEXT_MAP.Enable;
    const ToastName = !isNil(name)
      ? name
      : target === Target.Info
        ? 'INFO'
        : 'API';
    const DisableContent =
      target === Target.Info
        ? INFO_FE_TEXT_MAP.Disable_Error_Tips
        : API_FE_TEXT_MAP.Disable_Fail_Tips;
    const EnableContent =
      target === Target.Info
        ? INFO_FE_TEXT_MAP.Able_Error_Tips
        : API_FE_TEXT_MAP.Enable_Fail_Tips;
    try {
      // 调用启用禁用接口
      const res = await infoServiceClient.DisableOrEnableInfoOrApi({
        target,
        id,
        operateType: type,
        operateUserName: agent?.UserName || agent?.username,
        permissionPoint,
      });
      if (res?.BaseResp?.StatusCode === 0) {
        // 禁用/启用成功
        if (res?.result) {
          Toast.success(
            COMMON_FE_TEXT_MAP.Enable_Success(ToastName, ToastSuccess),
          );
          // 刷新数据
          await getData?.(dataProps.current);
          return;
        }
        // 禁用操作失败 （只有一种情况就是被调用，需要唤起弹窗）
        if (isAbleOrDisable === DisableOrAbleWords.Disable) {
          Modal.warning({
            title: COMMON_FE_TEXT_MAP.Disable_Error_Title(ToastName),
            content: DisableContent,
            okText: COMMON_FE_TEXT_MAP.Ok,
            cancelText: COMMON_FE_TEXT_MAP.Cancel,
            hasCancel: false,
          });
          return;
        }
        // 启用失败
        if (isAbleOrDisable === DisableOrAbleWords.Able) {
          Modal.warning({
            title: `${COMMON_FE_TEXT_MAP.Enable_Error_Title(name)}`,
            content: EnableContent,
            okText: COMMON_FE_TEXT_MAP.Ok,
            cancelText: COMMON_FE_TEXT_MAP.Cancel,
            hasCancel: false,
          });
          return;
        }
      } else {
        Toast.error(res?.BaseResp?.StatusMessage);
        return;
      }
    } catch (err) {
      console.error('operate err:', err);
    }
    // 出了报错导致的操作失败，toast提示
    Toast.error(COMMON_FE_TEXT_MAP.Enable_Error(name, isAbleOrDisable));
  };

  // @description: 点击启用/禁用按钮后执行的逻辑
  const handleOperate = (isAbleOrDisable: string, id: string, name): void => {
    const ToastName = !isNil(name)
      ? name
      : target === Target.Info
        ? 'INFO'
        : 'API';
    // 如果是点击禁用，不需要二次弹窗确认
    if (isAbleOrDisable === DisableOrAbleWords.Disable) {
      handleModal(isAbleOrDisable, id, ToastName);
    } else {
      // 点击启用，需要二次弹窗确认
      Modal.success({
        title: COMMON_FE_TEXT_MAP.Enable_Name(ToastName),
        content:
          target === Target.Api
            ? API_FE_TEXT_MAP.Enable_Tips
            : INFO_FE_TEXT_MAP.Enable_Tips,
        okText: COMMON_FE_TEXT_MAP.Enable,
        cancelText: COMMON_FE_TEXT_MAP.Cancel,
        onOk: () => handleModal(isAbleOrDisable, id, ToastName),
      });
    }
  };

  const handleFilter = (newFilter: any): void => {
    // 筛选项变化时，默认页码回到第一页
    dataProps.current.pageNo = 1;
    dataProps.current.pageSize = 20;
    const obj = {};
    // 将筛选项数据变成string类型，匹配后端接口数据结构
    Object.keys(newFilter).forEach(item => {
      obj[item] = transferFilterData(newFilter[item]);
    });
    dataProps.current.filterData = { ...obj };
    // 重新获取列表数据
    getData?.(dataProps.current);
  };

  // 获取操作列
  const getDropdownItems = (record): Array<any> => {
    // 如果后端传过来的type不是约定的type，做兜底处理
    const name = transferValue(record?.name);
    const status = record?.status?.enumCode;
    // 编辑
    const EditOperate = {
      node: 'item',
      name: COMMON_FE_TEXT_MAP.Edit,
      onClick: (): void => onJumpFormDetail({ id: record?.id }),
    };
    // 查看操作记录
    const ShowRecordsOperate = {
      node: 'item',
      name: COMMON_FE_TEXT_MAP.Operation_Record,
      onClick: (): void => onRecordsShow(record?.id),
    };
    // 删除
    const DeleteOperate = {
      node: 'item',
      name: COMMON_FE_TEXT_MAP.Delete,
      type: 'danger',
      onClick: (): void => handleDeleteModal(record?.id, name),
    };
    // 启用
    const EnableOperate = {
      node: 'item',
      name: COMMON_FE_TEXT_MAP.Enable,
      onClick: (): any =>
        handleOperate(DisableOrAbleWords.Able, record?.id, name),
    };
    // 禁用
    const DisableOperate = {
      node: 'item',
      name: COMMON_FE_TEXT_MAP.Disable,
      type: 'danger',
      onClick: (): any =>
        handleOperate(DisableOrAbleWords.Disable, record?.id, name),
    };
    // 分割线
    const Divider = { node: 'divider' };
    const OperateArr = [
      target === Target.Api ? EditOperate : null,
      ShowRecordsOperate,
      Divider,
    ].filter(item => !!item);

    if (target === Target.Api) {
      if (permissionPoint === PermissionPoint.Development) {
        if (status === StatusType.INCONFIG) {
          return OperateArr.concat([DeleteOperate]);
        }
        if (status === StatusType.DISABLE) {
          return OperateArr.concat([EnableOperate]);
        }
        if (status === StatusType.ENABLE) {
          return OperateArr.concat([DisableOperate]);
        }
      }
      return [ShowRecordsOperate];
    }
    if (target === Target.Info) {
      if (permissionPoint === PermissionPoint.Operate) {
        return [ShowRecordsOperate];
      }
      if (status === StatusType.INCONFIG) {
        if (permissionPoint === PermissionPoint.Development) {
          return OperateArr.concat([DeleteOperate]);
        }
        return [ShowRecordsOperate];
      }
      if (status === StatusType.DISABLE) {
        return OperateArr.concat([EnableOperate]);
      }
      if (status === StatusType.ENABLE) {
        return OperateArr.concat([DisableOperate]);
      }
    }
    return [ShowRecordsOperate];
  };

  // 将操作列添加自定义渲染组件
  const operateColumnsMap = {
    title: '',
    dataIndex: 'operate',
    fixed: 'right',
    width: target === Target.Api ? 56 : 150,
    render: (_, record: any): React.ReactElement => (
      <Space>
        {target === Target.Info && (
          <Space>
            <Typography.Text
              disabled={permissionPoint !== PermissionPoint.Development}
              link
              onClick={(): void => onJumpFormDetail({ id: record?.id })}
            >
              {COMMON_FE_TEXT_MAP.Edit}
            </Typography.Text>
            <Typography.Text
              link
              onClick={(): void => onJumpDebug({ id: record?.id })}
            >
              {COMMON_FE_TEXT_MAP.DEBUG}
            </Typography.Text>
          </Space>
        )}
        <Dropdown menu={getDropdownItems(record)}>
          <Button theme="borderless" icon={<IconMore />} />
        </Dropdown>
      </Space>
    ),
  };

  useEffect(() => {
    fetchSchemaAndData();

    const el = document.querySelector<HTMLDivElement>(
      '.united_info_management-light-scrollbar',
    );
    if (!el) {
      return;
    }

    // observe size changes on this element only
    const ro = new ResizeObserver(entries => {
      // there’ll typically be just one entry
      const entry = entries[0];
      setTableWidth(entry.contentRect.width - PAGE_MARGIN);
    });

    ro.observe(el);

    // cleanup
    return () => {
      ro.disconnect();
    };
  }, []);

  return {
    dataId,
    pageSchema,
    listData,
    isPageEmpty,
    pagination,
    selectSchema,
    tableLoading,
    recordsVisible,
    operateColumnsMap,
    tableWidth,
    onJumpDebug,
    handleFilter,
    onJumpFormDetail,
    onRecordsCancel,
    onRecordsShow,
    getDropdownItems,
  };
};
