import { LogAnalysisForm } from '@/components/log-analysis/log-form';
import useInfoLogAnalysis from './use-info-log-analysis';
import styles from './index.module.scss';
import { LogOverview } from '@/components/log-analysis/log-overview';
import { Banner, Tabs, TabPane, Typography, Space } from '@hi-design/ui';
import { LogRequestData } from '@/components/log-analysis/log-request-data';
import { LogRecord } from '@/components/log-analysis/log-record';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { NoData } from '@/components/log-analysis/no-data';
import { ReqExecNodeStatus } from '@/common/constants/info';
import { TabExtra } from '@/components/log-analysis/tab-extra';

export const LogAnalysis = () => {
  const {
    isSearching,
    formValues,
    hasSearchReturned,
    searchStatus,
    argosLink,
    currentInfoTab,
    allInfoKeys,
    selectedInfoKey,
    selectedPSM,
    filteredLogData,
    allPSMs,
    handleSearchValueChange,
    handleSelectInfoKey,
    handleSelectPSM,
    handleChangeTab,
    handleStopSearch,
    onSubmit,
  } = useInfoLogAnalysis();

  return (
    <div className={styles.logAnalysis}>
      <LogAnalysisForm
        isLoading={isSearching}
        hasSearchReturned={hasSearchReturned}
        isSearchSuccess={searchStatus.success}
        argosLink={argosLink}
        formValues={formValues}
        handleValueChange={handleSearchValueChange}
        handleStopSearch={handleStopSearch}
        onSubmit={onSubmit}
      />
      {hasSearchReturned ? (
        searchStatus.success ? (
          <Tabs
            collapsible
            className={styles.logResult}
            lazyRender
            keepDOM={false}
            activeKey={currentInfoTab}
            onChange={handleChangeTab}
            tabBarExtraContent={
              <TabExtra
                allInfoKeys={allInfoKeys}
                allPSMs={allPSMs}
                selectedInfoKey={selectedInfoKey}
                selectedPSM={selectedPSM}
                handleSelectInfoKey={handleSelectInfoKey}
                handleSelectPSM={handleSelectPSM}
              />
            }
          >
            {filteredLogData.map(item => (
              <TabPane
                key={item.requestId}
                itemKey={item.requestId}
                tab={
                  <div className={styles.tabHeader}>
                    <div className={styles.tabHeaderTitleContainer}>
                      <Typography.Text
                        className={`${styles.tabHeaderTitle} ${item.status === ReqExecNodeStatus.Success ? styles.success : item.status === ReqExecNodeStatus.Fail ? styles.fail : ''}`}
                      >
                        {INFO_DEBUG_TEXT_MAP.REQUEST_NUMBER(item.requestSeq)}
                      </Typography.Text>
                    </div>
                    <div>
                      <Typography.Paragraph
                        type="secondary"
                        size="small"
                        style={{ whiteSpace: 'pre-wrap' }}
                      >
                        {item.tabDesc}
                      </Typography.Paragraph>
                    </div>
                  </div>
                }
              >
                <div className={styles.tabContaienr}>
                  <section>
                    <Space className={styles.sectionHeader} spacing="medium">
                      <Typography.Title heading={6}>
                        {INFO_DEBUG_TEXT_MAP.EXECUTION_OVERVIEW}
                      </Typography.Title>
                      <Typography.Text type="tertiary">
                        {INFO_DEBUG_TEXT_MAP.REQ_ID(item.requestId)}
                      </Typography.Text>
                    </Space>

                    <LogOverview
                      callerPSM={item.callerPsm}
                      executionNodes={item.errorList}
                    />
                  </section>
                  <section>
                    <Typography.Title
                      heading={6}
                      className={styles.sectionHeader}
                      style={{ marginBottom: 0 }}
                    >
                      {INFO_DEBUG_TEXT_MAP.REQUEST_DATA}
                    </Typography.Title>
                    <LogRequestData
                      requestParams={item.requestParams}
                      infoKeyDiff={item.infoKeyDiff}
                      infoKeyResults={item.infoKeyResults}
                      activeInfoKey={selectedInfoKey}
                    />
                  </section>
                  <section>
                    <div className={styles.sectionHeader}>
                      <Typography.Title heading={6} style={{ marginBottom: 4 }}>
                        {INFO_DEBUG_TEXT_MAP.EXECUTION_RECORD}
                      </Typography.Title>
                      <Typography.Text type="tertiary" size="small">
                        {INFO_DEBUG_TEXT_MAP.EXECUTION_RECORD_DESC}
                      </Typography.Text>
                    </div>
                    {item?.errorList ? (
                      <LogRecord errorList={item?.errorList} />
                    ) : (
                      <NoData />
                    )}
                  </section>
                </div>
              </TabPane>
            ))}
          </Tabs>
        ) : (
          <Banner
            className={styles.banner}
            fullMode
            hideClose
            type="danger"
            description={
              searchStatus.reason || INFO_DEBUG_TEXT_MAP.LOG_NOT_FOUND
            }
          />
        )
      ) : null}
    </div>
  );
};
