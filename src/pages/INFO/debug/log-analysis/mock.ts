import { info_service } from '@/bam-auto-generate/infoServiceRPC';

export interface ErrorInfo {
  status: number;
  errorDetailList: string[];
  logs: info_service.LogEntry[];
}

export interface RequestDetailsV2 {
  timeStamp: string;
  requestParams: Record<string, string>;
  requestInfokeys: string[];
  infoKeyResults: info_service.InfoKeyResult[];
  errorList: Partial<Record<info_service.ErrorParty, ErrorInfo>>;
}

export interface CheckDMLogMileStoneResponseV2 {
  requestDetails: Record<string, RequestDetailsV2>;
  logFindSuccess: boolean;
  infoLogTip: string;
  logUrl: string;
}

export const mock: CheckDMLogMileStoneResponseV2 = {
  requestDetails: {
    '1a2b3c4d5e6f7890': {
      timeStamp: '1749132000000000',
      requestParams: {
        userId: 'user-5678',
        operation: 'FetchData',
      },
      requestInfokeys: ['Infokey A', 'InfokeyB'],
      infoKeyResults: [
        {
          InfoKey: 'Infokey A',
          InfoValue: '42',
          ValueSource: 0,
        },
        {
          InfoKey: 'InfokeyB',
          InfoValue: 'OK',
          ValueSource: 1,
        },
      ],
      errorList: {
        '0': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Received request from upstream service',
              pod: 'upstream-pod-xyz',
              ip: '********',
              spanId: 'span-up-0001',
              timeStamp: '1749131995000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamHandler.py:123',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Upstream request validated successfully',
              pod: 'upstream-pod-xyz',
              ip: '********',
              spanId: 'span-up-0002',
              timeStamp: '1749131997000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamHandler.py:145',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
      },
    },
    'req-001': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg:
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin sollicitudin lacinia ipsum, eget mollis magna fringilla ut. Proin tincidunt tellus eu neque venenatis pretium. Quisque tempor euismod quam a ornare. Curabitur et metus ut orci cursus auctor sit amet non felis. Duis fermentum nibh neque, a tempus libero dapibus nec. Aenean risus massa, facilisis at tristique quis, porttitor ac sapien. Suspendisse ultrices a nisi et laoreet. Integer a sagittis enim. Pellentesque bibendum euismod malesuada. Etiam feugiat dignissim arcu imperdiet rhoncus. Duis tortor diam, tristique auctor massa eu, interdum pretium orci.',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-002': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-003': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-004': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-005': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-006': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-007': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-008': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-009': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0010': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0011': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0012': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0013': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0014': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0015': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0016': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0017': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0018': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
    'req-0019': {
      timeStamp: '1749132070000000',
      requestParams: {
        query: 'SELECT * FROM table WHERE id=1234',
        retryCount: '0',
      },
      requestInfokeys: [
        '$.data.QueryPayinRefundByKey.Data[*].Currency',
        'InfokeyY',
        'InfokeyZ',
      ],
      infoKeyResults: [
        {
          InfoKey: '$.data.QueryPayinRefundByKey.Data[*].Currency',
          InfoValue: 'ValueFromCache',
          ValueSource: 2,
        },
        {
          InfoKey: 'InfokeyY',
          InfoValue: 'ValueFromDB',
          ValueSource: 1,
        },
        {
          InfoKey: 'InfokeyZ',
          InfoValue: 'ValueFromDefault',
          ValueSource: 0,
        },
      ],
      errorList: {
        '0': {
          status: 2,
          errorDetailList: [
            'span-up-0003: Timeout waiting for upstream response',
          ],
          logs: [
            {
              logMsg: 'Timed out waiting for upstream response',
              pod: 'upstream-pod-abc',
              ip: '********',
              spanId: 'span-up-0003',
              timeStamp: '1749132005000000',
              idc: 'GCP-asia-east1',
              psm: 'psm-upstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/upstreamClient.py:87',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
        '1': {
          status: 0,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'DataMarket stage entered',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0001',
              timeStamp: '1749132010000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:256',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DataMarket processed successfully',
              pod: 'dm-pod-001',
              ip: '*********',
              spanId: 'span-dm-0002',
              timeStamp: '1749132015000000',
              idc: 'GCP-us-central1',
              psm: 'psm-datamarket-01',
              logId: 'log-12345-ABCDE',
              location: '/srv/dm/processor.go:312',
              level: 'Debug',
              ifPpe: false,
            },
          ],
        },
        '2': {
          status: 1,
          errorDetailList: [],
          logs: [
            {
              logMsg: 'Started DM script transformation',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0001',
              timeStamp: '1749132020000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:50',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'DM script completed with Warnings',
              pod: 'dmscript-pod-12',
              ip: '*********',
              spanId: 'span-dm-s-0002',
              timeStamp: '1749132025000000',
              idc: 'GCP-us-central1',
              psm: 'psm-dmscript-02',
              logId: 'log-12345-ABCDE',
              location: '/scripts/transform.py:78',
              level: 'Warn',
              ifPpe: true,
            },
          ],
        },
        '3': {
          status: 2,
          errorDetailList: [
            'span-down-0001: Downstream RPC failure: connection refused',
            'span-down-0001: Retried 3 times, still failed',
          ],
          logs: [
            {
              logMsg: 'Calling downstream RPC service',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132030000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:134',
              level: 'Info',
              ifPpe: false,
            },
            {
              logMsg: 'Downstream RPC connection refused',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0001',
              timeStamp: '1749132035000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:148',
              level: 'Error',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #1',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0002',
              timeStamp: '1749132040000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #2',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0003',
              timeStamp: '1749132045000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Warn',
              ifPpe: true,
            },
            {
              logMsg: 'Downstream RPC retry attempt #3',
              pod: 'consumer-pod-77',
              ip: '*********',
              spanId: 'span-down-0004',
              timeStamp: '1749132050000000',
              idc: 'GCP-europe-west1',
              psm: 'psm-downstream-01',
              logId: 'log-12345-ABCDE',
              location: '/app/src/consumer/client.go:152',
              level: 'Error',
              ifPpe: true,
            },
          ],
        },
      },
    },
  },
  logFindSuccess: true,
  infoLogTip: 'All logs found for this invocation.',
  logUrl: 'https://logs.example.com/logs/log-12345-ABCDE',
};
