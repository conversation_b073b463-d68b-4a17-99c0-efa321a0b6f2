import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { LOG_REGION_OPTIONS, ServiceRegion } from '@/common/constants/info';
import { LogAnalysisForm } from '@/components/log-analysis/log-form';
import { useHistory } from '@edenx/plugin-router-v5/runtime';
import queryString from 'query-string';
import { format } from 'date-fns';
import { InfoDetail } from '@/types/info-debug';
import { useStores } from '@/stores';
import { infoServiceRpcService } from '@/common/http/infoServiceRPCService';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { Toast } from '@hi-design/ui';
import { ErrorResponse, isRequestCancelled } from '@/common/http';
import { isLogTimestampWithinMinutes } from '@/common/utils/time';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { RouterPath } from '@/const';

export interface FormattedLogData {
  requestId: string;
  requestSeq: number;
  tabDesc: string;
  requestParams: {
    title: string;
    value: string;
  }[];
  infoKeyResults: info_service.InfoKeyResult[];
  infoKeyDiff: string[];
  errorList: Partial<Record<info_service.ErrorParty, info_service.ErrorInfo>>;
  status: number;
  callerPsm: string;
}

export interface UseInfoLogAnalysisResult {
  isSearching: boolean;
  hasSearchReturned: boolean;
  searchStatus: {
    success: boolean;
    reason: string;
  };
  formValues: LogAnalysisForm;
  logData: info_service.CheckDMLogMileStoneV2Response | null;
  formattedLogData: FormattedLogData[];
  filteredLogData: FormattedLogData[];
  argosLink?: string;
  currentInfo: InfoDetail | null;
  currentInfoTab: string;
  selectedInfoKey: string;
  selectedPSM: string;
  allInfoKeys: string[];
  allPSMs: string[];
  handleSearchValueChange: (values: LogAnalysisForm) => void;
  handleSelectInfoKey: (value: string) => void;
  handleSelectPSM: (value: string) => void;
  handleChangeTab: (key: string) => void;
  handleStopSearch: () => void;
  onSubmit: () => void;
}

// eslint-disable-next-line max-lines-per-function
const useInfoLogAnalysis = (): UseInfoLogAnalysisResult => {
  const history = useHistory();
  const query = queryString.parse(history.location.search) || {};
  const { logID, region, psm } = query;
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearchReturned, setHasSearchReturned] = useState(false);
  const [searchStatus, setSearchStatus] = useState<{
    success: boolean;
    reason: string;
  }>({ success: true, reason: '' });
  const [logData, setLogData] =
    useState<info_service.CheckDMLogMileStoneV2Response | null>(null);
  const abortController = useRef<AbortController>();
  const { info } = useStores();
  const { currentInfo } = info || {};

  const [selectedInfoKey, setSelectedInfoKey] = useState<string>(
    currentInfo?.infoKey || '',
  );
  const [selectedPSM, setSelectedPSM] = useState<string>(psm as string);

  const [currentInfoTab, setCurrentInfoTab] = useState<string>('');

  const resetResult = () => {
    setLogData(null);
    setHasSearchReturned(false);
    setSelectedPSM(psm as string);
    setSelectedInfoKey(currentInfo?.infoKey || '');
  };

  const handleSelectInfoKey = (value: string) => {
    setSelectedInfoKey(value);
  };

  const handleSelectPSM = (value: string) => {
    setSelectedPSM(value);
  };

  const handleChangeTab = (key: string) => {
    setCurrentInfoTab(key);
  };

  const handleSearchLog = useCallback(async () => {
    resetResult();
    if (abortController.current) {
      return;
    }
    const controller = new AbortController();
    abortController.current = controller;
    try {
      setIsSearching(true);

      const res = await infoServiceRpcService.CheckDMLogMileStoneV2(
        {
          logId: logID as string,
          dataCenter: region as string,
          upstreamPsm: psm as string,
        },
        { signal: controller.signal },
      );

      setLogData(res.logFindSuccess ? res : null);
      setHasSearchReturned(true);
      setSearchStatus({
        success: res.logFindSuccess || false,
        reason:
          res.infoLogTip ||
          (isLogTimestampWithinMinutes(logID as string)
            ? INFO_DEBUG_TEXT_MAP.LOG_FETCH_FAIL_DELAY
            : ''),
      });
    } catch (error) {
      setHasSearchReturned(false);

      // Check if the request was cancelled
      if (isRequestCancelled(error)) {
        Toast.info('Log search was cancelled by user');
        console.log('Log search cancelled by user');
        return;
      }

      // Handle actual errors
      const err = error as ErrorResponse;
      Toast.error(
        `Failed to search log. ${err.statusMsg || ''} ${err.error || ''}`,
      );
      console.error(error);
    } finally {
      abortController.current = undefined;
      setIsSearching(false);
    }
  }, [logID, region, psm]);

  const formattedLogData: FormattedLogData[] = useMemo(() => {
    if (!logData?.requestDetails) {
      return [];
    }
    const requestData = Object.entries(logData.requestDetails || {})
      .sort((a, b) => a[1].timeStamp?.localeCompare(b[1].timeStamp || '') || 0)
      .map(([key, value]) => ({
        requestId: key,
        tabDesc: `${
          value.timeStamp
            ? format(
                new Date(Number(value.timeStamp.slice(0, -3))),
                'yyyy-MM-dd HH:mm:ss.SSS',
              )
            : '-'
        }`,
        requestParams: Object.entries(value.requestParams || {})
          .map(([key, value]) => ({
            title: key,
            value: String(value),
          }))
          .sort((a, b) => a.title.localeCompare(b.title)),
        infoKeyResults: value.infoKeyResults || [],
        infoKeyDiff:
          value.requestInfokeys?.filter(
            out =>
              !value.infoKeyResults?.some(inItem => inItem.InfoKey === out),
          ) || [],
        errorList: value.errorList || {},
        status: Math.max(
          ...Object.entries(value.errorList || {}).map(
            ([_, value]) => value?.status || 0,
          ),
        ),
        callerPsm: value.callerPsm || '-',
      }));

    return requestData.map((item, idx) => ({ requestSeq: idx + 1, ...item }));
  }, [logData]);

  const allInfoKeys = useMemo(
    () =>
      Array.from(
        new Set(
          formattedLogData
            .filter(item =>
              selectedPSM ? item.callerPsm === selectedPSM : true,
            )
            .map(item =>
              item.infoKeyResults
                ?.map(inItem => inItem.InfoKey)
                .concat(item.infoKeyDiff),
            )
            .flat(),
        ),
      ),
    [formattedLogData, selectedPSM],
  );

  const allPSMs = useMemo(
    () =>
      Array.from(
        new Set(
          formattedLogData
            .filter(item => {
              const infoKeys = item.infoKeyResults
                ?.map(inItem => inItem.InfoKey)
                .concat(item.infoKeyDiff);
              return selectedInfoKey
                ? infoKeys?.includes(selectedInfoKey)
                : true;
            })
            .map(item => item.callerPsm),
        ),
      ),
    [formattedLogData, selectedInfoKey],
  );

  const filteredLogData = useMemo(
    () =>
      formattedLogData.filter(
        item =>
          (!selectedInfoKey ||
            item.infoKeyResults?.some(
              inItem => inItem.InfoKey === selectedInfoKey,
            ) ||
            item.infoKeyDiff?.includes(selectedInfoKey)) &&
          (selectedPSM ? item.callerPsm === selectedPSM : true),
      ),
    [formattedLogData, selectedInfoKey, selectedPSM],
  );

  useEffect(() => {
    setCurrentInfoTab(filteredLogData[0]?.requestId || '');
  }, [filteredLogData]);

  useEffect(() => {
    if (!currentInfo?.infoKey) {
      return;
    }
    setSelectedInfoKey(currentInfo.infoKey);
  }, [currentInfo?.infoKey]);

  return {
    isSearching,
    hasSearchReturned,
    searchStatus,
    formValues: {
      logID: logID as string,
      region: LOG_REGION_OPTIONS.filter(
        item => item?.value === ServiceRegion.ROW,
      )?.[0]?.value,
      psm: psm as string,
    },
    logData,
    formattedLogData,
    argosLink: logData?.logUrl || '',
    currentInfo,
    selectedInfoKey,
    selectedPSM,
    filteredLogData,
    currentInfoTab,
    allInfoKeys,
    allPSMs,
    handleSearchValueChange: (values: LogAnalysisForm) => {
      const updatedQuery = {
        ...query,
        logID: values.logID,
        region: values.region,
        psm: values.psm,
      };

      const stringfied = queryString.stringify(updatedQuery);
      history.replace(`${RouterPath.INFO_DEBUG}?${stringfied}`);
    },
    handleStopSearch: () => {
      abortController.current?.abort();
      setIsSearching(false);
    },
    onSubmit: handleSearchLog,
    handleSelectInfoKey,
    handleSelectPSM,
    handleChangeTab,
  };
};
export default useInfoLogAnalysis;
