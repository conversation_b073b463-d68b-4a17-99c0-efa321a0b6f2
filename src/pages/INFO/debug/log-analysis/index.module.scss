.logAnalysis {
  .banner {
    margin-top: 12px;
  }

  .logResult {
    margin-top: 12px;

    .tabHeader {
      min-width: 150px;

      .tabHeaderTitleContainer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tabHeaderTitle {
          &::after {
            content: '';
            display: inline-block;
            margin-left: 4px;
            width: 8px;
            height: 8px;
            vertical-align: 2px;
            border-radius: 50%;
          }

          &.fail::after {
            background-color: var(--semi-color-danger);
          }
          &.success::after {
            background-color: var(--semi-color-success);
          }
        }

        .icon {
          transform: translateY(-4px);
          display: inline-block;
        }
      }
    }

    .tabContaienr {
      padding: 18px 12px 0 12px;
      display: flex;
      flex-direction: column;
      gap: 24px;

      .sectionHeader {
        margin-bottom: 16px;
      }
    }
  }
}
