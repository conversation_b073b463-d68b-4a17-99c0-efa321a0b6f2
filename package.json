{"name": "info_management_i18n_client", "repository": {"type": "git", "url": "******************:csp/info_management_i18n.git"}, "version": "1.0.0", "scripts": {"reset": "npx rimraf ./**/node_modules", "setup": "pnpm run reset && pnpm install", "dev": "edenx dev", "build": "edenx build", "start": "edenx start", "serve": "edenx serve", "new": "edenx new", "lint": "edenx lint", "lint:error": "edenx lint --quiet", "deploy": "edenx deploy", "prepare": "simple-git-hooks", "upgrade": "edenx upgrade"}, "engines": {"node": ">=14.17.6"}, "lint-staged": {"*.{ts,tsx}": ["node --max_old_space_size=8192 ./node_modules/eslint/bin/eslint.js --fix --color --cache --quiet"], "*.{js,jsx,mjs,mjsx,cjs,cjsx}": ["node --max_old_space_size=8192 ./node_modules/eslint/bin/eslint.js --fix --color --cache --quiet"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "eslintIgnore": ["node_modules/", "dist/", "stories", "gulux_gen", "http_idl"], "dependencies": {"@byted-ace/form": "1.1.10-beta.0", "@byted/ssa_watermark": "^2.3.3", "@dp/byted-tea-sdk-oversea": "^5.3.11", "@edenx/plugin-router-v5": "1.67.4", "@hi-design/ui": "^1.14.0", "@hi-design/ui-icons": "^1.14.0", "@hi-design/ui-illustrations": "^1.14.0", "@ies/ais": "2.1.6-beta.1", "@ies/csp-translate-gen-semi2": "^0.1.0", "@ies/intl-react-plugin": "^0.4.11", "@ies/semi-react-user-card": "^2.0.4", "@ies/starling_client": "^3.5.12", "@ies/starling_intl": "^1.5.9", "@ies/unified_communications_sdk": "^2.0.11", "@ies/united-sdk-i18n": "^0.1.12", "@semi-bot/semi-theme-bytehi-semi2": "^1.0.18", "@slardar/integrations": "^2.13.1", "@slardar/web": "^1.14.4", "axios": "^1.9.0", "classnames": "^2.5.1", "date-fns": "^4.1.0", "lodash-es": "^4.17.21", "mobx": "^6.13.7", "mobx-react": "^9.2.0", "murmurhash-js": "^1.0.0", "normalize.css": "^8.0.1", "query-string": "^9.1.2", "react": "~18.2.0", "react-dom": "~18.2.0", "react-infinite-scroller": "^1.2.6", "zod": "^3.22.3"}, "devDependencies": {"@byted-arch-fe/bam-code-generator": "^1.17.4", "@byted/eslint-config-standard": "^3.1.1", "@byted/eslint-config-standard-react": "^2.1.1", "@byted/eslint-config-standard-ts": "^3.1.1", "@edenx/app-tools": "1.67.4", "@edenx/plugin-slardar-web": "1.67.4", "@edenx/runtime": "1.67.4", "@edenx/tsconfig": "1.67.4", "@hi-design/builder-plugin": "^1.1.3", "@ies/argus-webpack-plugin": "^3.4.1", "@types/lodash-es": "^4.17.12", "@types/murmurhash-js": "^1.0.6", "@types/node": "~16.11.7", "@types/react": "~18.2.22", "@types/react-dom": "~18.2.7", "@types/react-infinite-scroller": "^1.2.5", "eslint": "^8.57.0", "eslint-plugin-prettier": "~5.2.2", "lint-staged": "~13.1.0", "prettier": "^3.4.2", "rimraf": "~3.0.2", "simple-git-hooks": "^2.11.1", "ts-node": "^10.9.2", "tsconfig-paths": "^3.14.1", "typescript": "~5.0.4"}}